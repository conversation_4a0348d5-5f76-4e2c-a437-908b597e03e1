#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务启动文件
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from log_service.service import LogService
from log_service.config import config


def main():
    """主函数"""
    print("🌟 启动Yuan项目日志服务")
    print(f"📍 服务端口: {config.service_port}")
    print(f"🗄️ 数据库路径: {config.database_path}")
    print(f"🏛️ 注册中心: {config.registry_url}")
    
    # 创建并启动服务
    service = LogService()
    service.run()


if __name__ == "__main__":
    main()
