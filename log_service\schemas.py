#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务API数据模型
"""

from typing import Optional, List, Dict, Any
from datetime import date, datetime
from pydantic import BaseModel, Field, validator


class LogRequest(BaseModel):
    """日志记录请求模型"""
    level: str = Field(..., description="日志级别")
    service_name: str = Field(..., description="服务名称")
    message: str = Field(..., description="日志消息")
    module: Optional[str] = Field(None, description="模块名称")
    function: Optional[str] = Field(None, description="函数名称")
    context: Optional[str] = Field(None, description="上下文信息")
    user_id: Optional[str] = Field(None, description="用户ID")
    request_id: Optional[str] = Field(None, description="请求ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")
    
    @validator('level')
    def validate_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
        return v.upper()
    
    class Config:
        schema_extra = {
            "example": {
                "level": "INFO",
                "service_name": "chat_service",
                "module": "chat_handler",
                "function": "process_message",
                "message": "开始处理用户消息",
                "context": "用户ID: user123",
                "user_id": "user123",
                "request_id": "req-456",
                "metadata": {"message_length": 50, "model": "gpt-3.5-turbo"}
            }
        }


class LogBatchRequest(BaseModel):
    """批量日志记录请求模型"""
    logs: List[LogRequest] = Field(..., description="日志列表", min_items=1, max_items=100)

    class Config:
        schema_extra = {
            "example": {
                "logs": [
                    {
                        "level": "INFO",
                        "service_name": "chat_service",
                        "message": "用户登录",
                        "user_id": "user123"
                    },
                    {
                        "level": "DEBUG",
                        "service_name": "chat_service",
                        "message": "处理消息",
                        "user_id": "user123"
                    }
                ]
            }
        }


class LogQueryRequest(BaseModel):
    """日志查询请求模型"""
    start_time: Optional[float] = Field(None, description="开始时间戳")
    end_time: Optional[float] = Field(None, description="结束时间戳")
    service_name: Optional[str] = Field(None, description="服务名称")
    level: Optional[str] = Field(None, description="日志级别")
    limit: Optional[int] = Field(50, description="返回数量限制", ge=1, le=1000)
    offset: Optional[int] = Field(0, description="分页偏移量", ge=0)
    
    @validator('level')
    def validate_level(cls, v):
        """验证日志级别"""
        if v is not None:
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if v.upper() not in valid_levels:
                raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
            return v.upper()
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "start_time": **********.0,
                "end_time": 1641081600.0,
                "service_name": "chat_service",
                "level": "ERROR",
                "limit": 100,
                "offset": 0
            }
        }


class StatsQueryRequest(BaseModel):
    """统计查询请求模型"""
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    service_name: Optional[str] = Field(None, description="服务名称")
    
    class Config:
        schema_extra = {
            "example": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
                "service_name": "chat_service"
            }
        }


class LogRecordResponse(BaseModel):
    """日志记录响应模型"""
    id: int
    timestamp: float
    datetime: str
    level: str
    service_name: str
    module: Optional[str]
    function: Optional[str]
    message: str
    context: Optional[str]
    user_id: Optional[str]
    request_id: Optional[str]
    metadata: Optional[Dict[str, Any]]
    created_at: float


class LogStatsResponse(BaseModel):
    """日志统计响应模型"""
    id: int
    date: str
    service_name: str
    level: str
    count: int
    updated_at: float


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    timestamp: float


class LogCreateResponse(BaseResponse):
    """日志创建响应模型"""
    data: Dict[str, Any] = Field(..., description="响应数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "log_id": 123,
                    "message": "日志记录成功"
                },
                "message": "日志记录成功",
                "timestamp": **********.0
            }
        }


class LogListResponse(BaseResponse):
    """日志列表响应模型"""
    data: Dict[str, Any] = Field(..., description="日志列表数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "logs": [],
                    "total": 100,
                    "limit": 50,
                    "offset": 0,
                    "has_more": True
                },
                "message": "查询日志成功",
                "timestamp": **********.0
            }
        }


class StatsListResponse(BaseResponse):
    """统计列表响应模型"""
    data: Dict[str, Any] = Field(..., description="统计数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "stats": [],
                    "summary": {
                        "total_logs": 1000,
                        "level_stats": {
                            "INFO": 500,
                            "ERROR": 50,
                            "WARNING": 100
                        }
                    }
                },
                "message": "获取统计数据成功",
                "timestamp": **********.0
            }
        }


class ServicesListResponse(BaseResponse):
    """服务列表响应模型"""
    data: Dict[str, Any] = Field(..., description="服务列表数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "services": ["chat_service", "log_service", "memory_service"],
                    "count": 3
                },
                "message": "获取服务列表成功",
                "timestamp": **********.0
            }
        }


class SummaryStatsResponse(BaseResponse):
    """汇总统计响应模型"""
    data: Dict[str, Any] = Field(..., description="汇总统计数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "total_logs": 1000,
                    "level_stats": {
                        "DEBUG": 100,
                        "INFO": 500,
                        "WARNING": 200,
                        "ERROR": 150,
                        "CRITICAL": 50
                    },
                    "service_stats": {
                        "chat_service": 600,
                        "log_service": 200,
                        "memory_service": 200
                    },
                    "latest_log_time": **********.0,
                    "services_count": 3
                },
                "message": "获取汇总统计成功",
                "timestamp": **********.0
            }
        }


class LogBatchResponse(BaseResponse):
    """批量日志创建响应模型"""
    data: Dict[str, Any] = Field(..., description="批量响应数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "log_ids": [123, 124, 125],
                    "created_count": 3,
                    "message": "批量日志记录成功"
                },
                "message": "批量日志记录成功",
                "timestamp": **********.0
            }
        }


class DatabaseInfoResponse(BaseResponse):
    """数据库信息响应模型"""
    data: Dict[str, Any] = Field(..., description="数据库信息")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "database_path": "./data/log_service.db",
                    "database_url": "sqlite:///./data/log_service.db",
                    "tables": {
                        "logs": {"count": 1000, "table_name": "logs"},
                        "log_stats": {"count": 50, "table_name": "log_stats"}
                    },
                    "total_records": 1050
                },
                "message": "获取数据库信息成功",
                "timestamp": **********.0
            }
        }


class CleanupPreviewResponse(BaseResponse):
    """清理预览响应模型"""
    data: Dict[str, Any] = Field(..., description="清理预览数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "dry_run": True,
                    "would_delete": 100,
                    "before_timestamp": **********.0,
                    "before_datetime": "2022-01-01T00:00:00"
                },
                "message": "清理预览完成",
                "timestamp": **********.0
            }
        }


class HealthCheckResponse(BaseResponse):
    """健康检查响应模型"""
    data: Dict[str, Any] = Field(..., description="健康检查数据")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "status": "healthy",
                    "service": "log_service",
                    "database_connected": True,
                    "uptime": 3600.0
                },
                "message": "服务运行正常",
                "timestamp": **********.0
            }
        }


# 工具函数和常量
VALID_LOG_LEVELS = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']


def validate_log_level(level: str) -> str:
    """验证并标准化日志级别"""
    if level.upper() not in VALID_LOG_LEVELS:
        raise ValueError(f'日志级别必须是: {", ".join(VALID_LOG_LEVELS)}')
    return level.upper()


def create_error_response(message: str, error_code: str = None) -> Dict[str, Any]:
    """创建错误响应"""
    import time
    response = {
        "success": False,
        "message": message,
        "timestamp": time.time()
    }
    if error_code:
        response["error_code"] = error_code
    return response


def create_success_response(data: Any, message: str = "操作成功") -> Dict[str, Any]:
    """创建成功响应"""
    import time
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": time.time()
    }


def format_timestamp(timestamp: float) -> str:
    """格式化时间戳为ISO格式"""
    return datetime.fromtimestamp(timestamp).isoformat()


def parse_time_range(start_time: Optional[str], end_time: Optional[str]) -> tuple:
    """解析时间范围字符串为时间戳"""
    start_ts = None
    end_ts = None

    if start_time:
        try:
            start_ts = datetime.fromisoformat(start_time.replace('Z', '+00:00')).timestamp()
        except ValueError:
            raise ValueError(f"无效的开始时间格式: {start_time}")

    if end_time:
        try:
            end_ts = datetime.fromisoformat(end_time.replace('Z', '+00:00')).timestamp()
        except ValueError:
            raise ValueError(f"无效的结束时间格式: {end_time}")

    return start_ts, end_ts
