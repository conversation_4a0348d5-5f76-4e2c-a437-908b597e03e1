#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务API数据模型 - 简化版
专注核心功能：快速记录和基础查询
"""

import time
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class LogRequest(BaseModel):
    """日志记录请求模型"""
    level: str = Field(..., description="日志级别")
    service_name: str = Field(..., description="服务名称") 
    message: str = Field(..., description="日志消息")
    module: Optional[str] = Field(None, description="模块名称")
    function: Optional[str] = Field(None, description="函数名称")
    context: Optional[str] = Field(None, description="上下文信息")


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    timestamp: float = Field(default_factory=time.time)


class LogResponse(BaseResponse):
    """日志记录响应模型"""
    data: Dict[str, Any]


class LogListResponse(BaseResponse):
    """日志查询响应模型"""
    data: Dict[str, Any]


# 简单的验证函数
def validate_log_level(level: str) -> str:
    """验证日志级别"""
    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    level_upper = level.upper()
    if level_upper not in valid_levels:
        raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
    return level_upper


def create_success_response(data: Any, message: str = "操作成功") -> Dict[str, Any]:
    """创建成功响应"""
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": time.time()
    }


def create_error_response(message: str) -> Dict[str, Any]:
    """创建错误响应"""
    return {
        "success": False,
        "message": message,
        "timestamp": time.time()
    }
