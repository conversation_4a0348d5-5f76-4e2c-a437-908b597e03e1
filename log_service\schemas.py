#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务API数据模型
"""

from typing import Optional, List, Dict, Any
from datetime import date
from pydantic import BaseModel, Field, validator


class LogRequest(BaseModel):
    """日志记录请求模型"""
    level: str = Field(..., description="日志级别")
    service_name: str = Field(..., description="服务名称")
    message: str = Field(..., description="日志消息")
    module: Optional[str] = Field(None, description="模块名称")
    function: Optional[str] = Field(None, description="函数名称")
    context: Optional[str] = Field(None, description="上下文信息")
    user_id: Optional[str] = Field(None, description="用户ID")
    request_id: Optional[str] = Field(None, description="请求ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")
    
    @validator('level')
    def validate_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
        return v.upper()
    
    class Config:
        schema_extra = {
            "example": {
                "level": "INFO",
                "service_name": "chat_service",
                "module": "chat_handler",
                "function": "process_message",
                "message": "开始处理用户消息",
                "context": "用户ID: user123",
                "user_id": "user123",
                "request_id": "req-456",
                "metadata": {"message_length": 50, "model": "gpt-3.5-turbo"}
            }
        }


class LogQueryRequest(BaseModel):
    """日志查询请求模型"""
    start_time: Optional[float] = Field(None, description="开始时间戳")
    end_time: Optional[float] = Field(None, description="结束时间戳")
    service_name: Optional[str] = Field(None, description="服务名称")
    level: Optional[str] = Field(None, description="日志级别")
    limit: Optional[int] = Field(50, description="返回数量限制", ge=1, le=1000)
    offset: Optional[int] = Field(0, description="分页偏移量", ge=0)
    
    @validator('level')
    def validate_level(cls, v):
        """验证日志级别"""
        if v is not None:
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if v.upper() not in valid_levels:
                raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
            return v.upper()
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "start_time": 1640995200.0,
                "end_time": 1641081600.0,
                "service_name": "chat_service",
                "level": "ERROR",
                "limit": 100,
                "offset": 0
            }
        }


class StatsQueryRequest(BaseModel):
    """统计查询请求模型"""
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    service_name: Optional[str] = Field(None, description="服务名称")
    
    class Config:
        schema_extra = {
            "example": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
                "service_name": "chat_service"
            }
        }


class LogRecordResponse(BaseModel):
    """日志记录响应模型"""
    id: int
    timestamp: float
    datetime: str
    level: str
    service_name: str
    module: Optional[str]
    function: Optional[str]
    message: str
    context: Optional[str]
    user_id: Optional[str]
    request_id: Optional[str]
    metadata: Optional[Dict[str, Any]]
    created_at: float


class LogStatsResponse(BaseModel):
    """日志统计响应模型"""
    id: int
    date: str
    service_name: str
    level: str
    count: int
    updated_at: float


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    timestamp: float


class LogCreateResponse(BaseResponse):
    """日志创建响应模型"""
    data: Dict[str, Any] = Field(..., description="响应数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "log_id": 123,
                    "message": "日志记录成功"
                },
                "message": "日志记录成功",
                "timestamp": 1640995200.0
            }
        }


class LogListResponse(BaseResponse):
    """日志列表响应模型"""
    data: Dict[str, Any] = Field(..., description="日志列表数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "logs": [],
                    "total": 100,
                    "limit": 50,
                    "offset": 0,
                    "has_more": True
                },
                "message": "查询日志成功",
                "timestamp": 1640995200.0
            }
        }


class StatsListResponse(BaseResponse):
    """统计列表响应模型"""
    data: Dict[str, Any] = Field(..., description="统计数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "stats": [],
                    "summary": {
                        "total_logs": 1000,
                        "level_stats": {
                            "INFO": 500,
                            "ERROR": 50,
                            "WARNING": 100
                        }
                    }
                },
                "message": "获取统计数据成功",
                "timestamp": 1640995200.0
            }
        }


class ServicesListResponse(BaseResponse):
    """服务列表响应模型"""
    data: Dict[str, Any] = Field(..., description="服务列表数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "services": ["chat_service", "log_service", "memory_service"],
                    "count": 3
                },
                "message": "获取服务列表成功",
                "timestamp": 1640995200.0
            }
        }


class SummaryStatsResponse(BaseResponse):
    """汇总统计响应模型"""
    data: Dict[str, Any] = Field(..., description="汇总统计数据")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "total_logs": 1000,
                    "level_stats": {
                        "DEBUG": 100,
                        "INFO": 500,
                        "WARNING": 200,
                        "ERROR": 150,
                        "CRITICAL": 50
                    },
                    "service_stats": {
                        "chat_service": 600,
                        "log_service": 200,
                        "memory_service": 200
                    },
                    "latest_log_time": 1640995200.0,
                    "services_count": 3
                },
                "message": "获取汇总统计成功",
                "timestamp": 1640995200.0
            }
        }
