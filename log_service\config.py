#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务配置文件
"""

import os
from pydantic import BaseSettings


class LogServiceConfig(BaseSettings):
    """日志服务配置类"""
    
    # 服务基础配置
    service_name: str = "log_service"
    service_port: int = 8003
    service_host: str = "127.0.0.1"
    
    # 服务注册中心配置
    registry_url: str = "http://127.0.0.1:8500"
    
    # 数据库配置
    database_url: str = "sqlite:///./data/log_service.db"
    database_path: str = "./data/log_service.db"
    
    # 日志配置
    log_level: str = "INFO"
    max_log_size: int = 100 * 1024 * 1024  # 100MB
    
    # 查询配置
    default_page_size: int = 50
    max_page_size: int = 1000
    
    # 统计配置
    stats_cache_ttl: int = 300  # 5分钟缓存

    # 性能配置
    batch_size: int = 100  # 批量处理大小
    connection_pool_size: int = 5  # 数据库连接池大小
    query_timeout: int = 30  # 查询超时时间（秒）

    # 日志级别映射
    log_levels: list = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

    # 服务元数据
    service_version: str = "1.0.0"
    service_description: str = "Yuan项目日志服务"

    class Config:
        env_prefix = "LOG_SERVICE_"
        case_sensitive = False


# 全局配置实例
config = LogServiceConfig()


def ensure_data_directory():
    """确保数据目录存在"""
    data_dir = os.path.dirname(config.database_path)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir, exist_ok=True)
        print(f"✅ 创建数据目录: {data_dir}")


def validate_config():
    """验证配置参数"""
    errors = []

    # 验证端口范围
    if not (1024 <= config.service_port <= 65535):
        errors.append(f"服务端口 {config.service_port} 不在有效范围内 (1024-65535)")

    # 验证分页配置
    if config.default_page_size <= 0:
        errors.append("默认分页大小必须大于0")

    if config.max_page_size < config.default_page_size:
        errors.append("最大分页大小不能小于默认分页大小")

    # 验证数据库路径
    if not config.database_path:
        errors.append("数据库路径不能为空")

    if errors:
        raise ValueError(f"配置验证失败: {'; '.join(errors)}")

    print("✅ 配置验证通过")


def get_service_metadata():
    """获取服务元数据"""
    return {
        "service_name": config.service_name,
        "version": config.service_version,
        "description": config.service_description,
        "port": config.service_port,
        "host": config.service_host,
        "database_path": config.database_path,
        "supported_levels": config.log_levels
    }
