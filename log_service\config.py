#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务配置文件
"""

import os
from pydantic import BaseSettings


class LogServiceConfig(BaseSettings):
    """日志服务配置类"""
    
    # 服务基础配置
    service_name: str = "log_service"
    service_port: int = 8003
    service_host: str = "127.0.0.1"
    
    # 服务注册中心配置
    registry_url: str = "http://127.0.0.1:8500"
    
    # 数据库配置
    database_url: str = "sqlite:///./data/log_service.db"
    database_path: str = "./data/log_service.db"
    
    # 日志配置
    log_level: str = "INFO"
    max_log_size: int = 100 * 1024 * 1024  # 100MB
    
    # 查询配置
    default_page_size: int = 50
    max_page_size: int = 1000
    
    # 统计配置
    stats_cache_ttl: int = 300  # 5分钟缓存
    
    class Config:
        env_prefix = "LOG_SERVICE_"
        case_sensitive = False


# 全局配置实例
config = LogServiceConfig()


def ensure_data_directory():
    """确保数据目录存在"""
    data_dir = os.path.dirname(config.database_path)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir, exist_ok=True)
        print(f"✅ 创建数据目录: {data_dir}")
