# Yuan项目日志模块设计

## 模块概述

### 功能定位
日志模块（Log Service）是Yuan项目的核心基础服务，负责统一收集、存储和管理所有微服务的日志信息，为系统监控、问题排查和运行分析提供支持。

### 设计原则
- **统一收集**：所有微服务的日志统一通过日志服务记录
- **简洁实用**：功能设计以实用性为主，避免过度复杂
- **高可靠性**：确保日志记录的准确性和完整性
- **易于查询**：提供便捷的日志查询和分析功能

## 服务规格

### 基础信息
- **服务名称**：log_service
- **服务端口**：8003
- **数据库文件**：`data/log_service.db`
- **服务类型**：核心业务服务

### 技术架构
- **基础框架**：继承BaseService，遵循微服务架构规范
- **数据存储**：独立SQLite数据库
- **通信方式**：RESTful API，同步调用
- **集成方式**：其他服务主动调用日志API

## 功能设计

### 核心功能
1. **日志记录**：接收并存储来自各微服务的日志数据
2. **日志查询**：提供基础的日志查询和过滤功能
3. **统计分析**：生成日志统计信息和分析报告
4. **服务监控**：记录服务运行状态和性能指标

### 日志级别
按照项目开发规范定义的五个级别：
- **DEBUG**：调试信息，开发阶段使用
- **INFO**：一般信息，正常业务流程记录
- **WARNING**：警告信息，需要注意但不影响运行
- **ERROR**：错误信息，业务处理失败
- **CRITICAL**：严重错误，系统级别问题

### 日志格式
标准化的日志数据结构：
```
{
    "timestamp": "时间戳",
    "level": "日志级别",
    "service_name": "服务名称",
    "module": "模块名称",
    "function": "函数名称",
    "message": "日志消息",
    "context": "上下文信息",
    "user_id": "用户ID（可选）",
    "request_id": "请求ID（可选）",
    "metadata": "额外元数据（JSON格式）"
}
```

## 数据库设计

### 主要数据表

#### logs表
存储所有日志记录的主表：
- `id`：主键，自增ID
- `timestamp`：时间戳，记录时间
- `level`：日志级别
- `service_name`：服务名称
- `module`：模块名称
- `function`：函数名称
- `message`：日志消息内容
- `context`：上下文信息
- `user_id`：用户ID（可选）
- `request_id`：请求ID（可选）
- `metadata`：额外元数据（JSON格式）
- `created_at`：创建时间

#### log_stats表
存储日志统计信息：
- `id`：主键
- `date`：统计日期
- `service_name`：服务名称
- `level`：日志级别
- `count`：日志数量
- `updated_at`：更新时间

### 索引设计
- 时间戳索引：提高按时间查询的性能
- 服务名称索引：提高按服务查询的性能
- 日志级别索引：提高按级别过滤的性能
- 复合索引：(service_name, level, timestamp)

## API接口设计

### 核心接口

#### POST /logs
记录单条日志
- **功能**：接收并存储日志数据
- **参数**：日志对象（JSON格式）
- **返回**：操作结果和日志ID

#### GET /logs
查询日志记录
- **功能**：根据条件查询日志
- **参数**：
  - `start_time`：开始时间
  - `end_time`：结束时间
  - `service_name`：服务名称
  - `level`：日志级别
  - `limit`：返回数量限制
  - `offset`：分页偏移量
- **返回**：日志列表和总数

#### GET /logs/stats
获取日志统计
- **功能**：获取日志统计信息
- **参数**：
  - `start_date`：开始日期
  - `end_date`：结束日期
  - `service_name`：服务名称（可选）
- **返回**：统计数据

#### GET /logs/services
获取服务列表
- **功能**：获取所有记录过日志的服务列表
- **返回**：服务名称列表

### 标准接口
继承BaseService的标准接口：
- `GET /health`：健康检查
- `GET /info`：服务信息

## 集成方式

### 服务间调用
其他微服务通过BaseService的`call_service`方法调用日志服务：
```python
# 记录日志示例
await self.call_service("log_service", "/logs", "POST", {
    "level": "INFO",
    "service_name": "chat_service",
    "module": "chat_handler",
    "function": "process_message",
    "message": "开始处理用户消息",
    "context": {"user_id": "user123", "message_length": 50}
})
```

### 便捷工具函数
在BaseService中提供便捷的日志记录方法：
```python
async def log_info(self, message, **kwargs):
    # 简化的日志记录方法
    
async def log_error(self, message, **kwargs):
    # 错误日志记录方法
```

## 查询和管理

### 程序化查询
通过API接口进行程序化查询，支持：
- 时间范围过滤
- 服务名称过滤
- 日志级别过滤
- 关键词搜索
- 分页查询

### 数据库工具查询
利用现有的数据库管理工具进行复杂查询：
- 灵活的SQL查询
- 数据导出功能
- 可视化数据浏览
- 手动数据清理

### 日志保留策略
- **无自动清理**：不设置自动日志清理机制
- **手动管理**：通过数据库管理工具手动清理旧日志
- **灵活控制**：可以根据需要保留重要日志
- **存储监控**：定期检查日志存储空间使用情况

## 性能考虑

### 写入性能
- **同步写入**：确保日志记录的可靠性
- **数据库优化**：合理的索引设计提高写入效率
- **批量处理**：预留批量日志接口，未来可扩展

### 查询性能
- **索引优化**：针对常用查询条件建立索引
- **分页查询**：避免大量数据的一次性加载
- **缓存策略**：统计数据可以考虑缓存机制

### 存储管理
- **数据压缩**：SQLite自带压缩功能
- **定期维护**：通过数据库工具进行表优化
- **备份策略**：定期备份日志数据库

## 扩展规划

### 功能扩展
- **日志告警**：未来可添加基于规则的日志告警功能
- **实时监控**：支持实时日志流监控
- **日志分析**：增强的日志分析和报表功能
- **日志聚合**：支持多维度的日志聚合分析

### 性能扩展
- **批量接口**：支持批量日志提交
- **异步处理**：可选的异步日志处理模式
- **分布式存储**：支持日志数据的分布式存储

## 依赖关系

### 服务依赖
- **Service Registry**：服务注册和发现
- **BaseService**：微服务基础功能

### 被依赖服务
- **Chat Service**：记录对话处理日志
- **Memory Service**：记录记忆管理日志
- **File Service**：记录文件操作日志
- **其他服务**：所有微服务都可能需要日志记录

### 外部依赖
- **SQLite**：数据存储
- **FastAPI**：Web框架
- **数据库管理工具**：日志查询和管理

## 开发优先级

### 第一阶段：核心功能
1. 基础日志记录API
2. 数据库模型和表结构
3. 基础查询功能
4. 服务注册和健康检查

### 第二阶段：查询优化
1. 高级查询功能
2. 统计分析接口
3. 性能优化
4. 与其他服务的集成测试

### 第三阶段：管理功能
1. 日志管理工具集成
2. 数据导出功能
3. 监控和告警（可选）
4. 文档完善和测试
