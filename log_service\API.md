# 日志服务API文档

## 接口列表

### 1. 记录日志
- **接口**: `POST /logs`
- **功能**: 记录日志到数据库
- **参数**:
```json
{
    "level": "INFO",
    "service_name": "chat_service", 
    "message": "用户登录",
    "module": "auth",
    "function": "login",
    "context": "用户ID: 123"
}
```
- **返回**:
```json
{
    "success": true,
    "data": {"log_id": 1, "level": "INFO", "service_name": "chat_service"},
    "message": "日志记录成功",
    "timestamp": 1640995200.0
}
```

### 2. 查询日志
- **接口**: `GET /logs`
- **功能**: 查询日志记录
- **参数**: 
  - service_name: 服务名称（可选）
  - level: 日志级别（可选）
  - limit: 返回数量（默认50，最大500）
- **返回**:
```json
{
    "success": true,
    "data": {"logs": [], "count": 0},
    "message": "查询到 0 条日志",
    "timestamp": 1640995200.0
}
```

## 调用方式
其他微服务通过BaseService的call_service方法调用：
```python
await self.call_service("log_service", "/logs", "POST", {
    "level": "INFO",
    "service_name": "chat_service",
    "message": "操作完成"
})
```
