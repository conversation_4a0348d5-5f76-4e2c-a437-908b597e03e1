# Yuan项目服务端口分配表

## 端口分配原则

- **8500**：服务注册中心（Service Registry）
- **8001-8099**：核心业务服务
- **8100-8199**：扩展业务服务
- **8200-8299**：工具和辅助服务
- **8300-8399**：第三方集成服务
- **8400-8499**：开发和测试服务

## 当前端口分配

### 核心基础设施
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8500 | service_registry | 服务注册中心 | ✅ 已实现 | `service_registry_service/` |

### 核心业务服务 (8001-8099)
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8001 | database_service | 数据库服务（已废弃） | ❌ 已移除 | - |
| 8002 | chat_service | AI对话服务 | ✅ 已实现 | `chat_service/` |
| 8003 | log_service | 日志服务 | ✅ 已实现 | `log_service/` |
| 8004 | memory_service | 记忆管理服务 | 🚧 规划中 | `memory_service/` |
| 8005 | file_service | 文件管理服务 | 🚧 规划中 | `file_service/` |
| 8006 | config_service | 配置管理服务 | 🚧 规划中 | `config_service/` |
| 8007 | user_service | 用户管理服务 | 📋 待规划 | `user_service/` |
| 8008 | auth_service | 认证授权服务 | 📋 待规划 | `auth_service/` |
| 8009 | notification_service | 通知服务 | 📋 待规划 | `notification_service/` |
| 8010 | workflow_service | 工作流服务 | 📋 待规划 | `workflow_service/` |

### 扩展业务服务 (8100-8199)
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8100 | search_service | 搜索服务 | 📋 待规划 | `search_service/` |
| 8101 | analytics_service | 分析统计服务 | 📋 待规划 | `analytics_service/` |
| 8102 | backup_service | 备份服务 | 📋 待规划 | `backup_service/` |
| 8103 | scheduler_service | 定时任务服务 | 📋 待规划 | `scheduler_service/` |
| 8104 | cache_service | 缓存服务 | 📋 待规划 | `cache_service/` |
| 8105 | queue_service | 消息队列服务 | 📋 待规划 | `queue_service/` |

### 工具和辅助服务 (8200-8299)
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8200 | monitoring_service | 监控服务 | 📋 待规划 | `monitoring_service/` |
| 8201 | health_service | 健康检查服务 | 📋 待规划 | `health_service/` |
| 8202 | metrics_service | 指标收集服务 | 📋 待规划 | `metrics_service/` |
| 8203 | trace_service | 链路追踪服务 | 📋 待规划 | `trace_service/` |
| 8204 | debug_service | 调试服务 | 📋 待规划 | `debug_service/` |

### 第三方集成服务 (8300-8399)
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8300 | openai_service | OpenAI API集成 | 📋 待规划 | `openai_service/` |
| 8301 | email_service | 邮件服务集成 | 📋 待规划 | `email_service/` |
| 8302 | sms_service | 短信服务集成 | 📋 待规划 | `sms_service/` |
| 8303 | payment_service | 支付服务集成 | 📋 待规划 | `payment_service/` |
| 8304 | storage_service | 云存储服务集成 | 📋 待规划 | `storage_service/` |

### 开发和测试服务 (8400-8499)
| 端口 | 服务名称 | 服务描述 | 状态 | 目录 |
|------|----------|----------|------|------|
| 8400 | test_service | 测试服务 | 📋 待规划 | `test_service/` |
| 8401 | mock_service | Mock服务 | 📋 待规划 | `mock_service/` |
| 8402 | dev_tools_service | 开发工具服务 | 📋 待规划 | `dev_tools_service/` |

## 端口申请流程

### 新服务端口申请
1. **确定服务类型**：根据服务功能确定端口范围
2. **检查端口可用性**：确认目标端口未被占用
3. **更新分配表**：在本文档中记录端口分配
4. **创建服务目录**：按照规范创建服务目录
5. **实现服务**：按照开发规范实现服务

### 端口冲突处理
1. **检测冲突**：启动时检测端口是否被占用
2. **错误报告**：明确报告端口冲突信息
3. **解决方案**：
   - 停止冲突服务
   - 重新分配端口
   - 更新配置文件

## 服务发现配置

### 服务注册信息
每个服务注册时提供的标准信息：

```json
{
    "service_name": "服务名称",
    "host": "127.0.0.1",
    "port": 8002,
    "health_check_url": "http://127.0.0.1:8002/health",
    "metadata": {
        "version": "1.0.0",
        "description": "服务描述",
        "category": "core|extended|tool|integration|dev",
        "capabilities": ["功能1", "功能2"],
        "dependencies": ["依赖服务1", "依赖服务2"]
    }
}
```

### 服务分类
- **core**：核心业务服务
- **extended**：扩展业务服务
- **tool**：工具和辅助服务
- **integration**：第三方集成服务
- **dev**：开发和测试服务

## 网络配置

### 本地开发环境
- **主机地址**：127.0.0.1
- **网络模式**：本地回环
- **防火墙**：无需配置

### 生产环境
- **主机地址**：内网IP或公网IP
- **网络模式**：Docker网络或Kubernetes网络
- **防火墙**：配置相应端口开放
- **负载均衡**：nginx或云负载均衡器

## 监控和管理

### 端口监控
- 监控端口占用情况
- 检测端口冲突
- 记录端口使用历史

### 服务健康检查
- 定期检查服务端口可达性
- 验证服务响应正常
- 记录服务可用性统计

### 端口使用统计
- 统计各端口范围使用情况
- 分析服务分布和负载
- 优化端口分配策略

## 安全考虑

### 端口安全
- 限制外部访问内部服务端口
- 使用防火墙规则保护敏感服务
- 定期审查端口开放情况

### 服务间通信安全
- 实现服务间认证
- 使用HTTPS加密通信
- 限制服务调用权限

## 扩展规划

### 端口扩展
- 预留足够的端口范围
- 支持动态端口分配
- 考虑容器化部署需求

### 服务扩展
- 支持服务水平扩展
- 实现服务版本管理
- 支持蓝绿部署

---

**注意事项**：
1. 新增服务时必须更新此端口分配表
2. 端口分配遵循功能分类原则
3. 避免端口冲突，确保服务正常启动
4. 生产环境部署时需要考虑网络安全配置
