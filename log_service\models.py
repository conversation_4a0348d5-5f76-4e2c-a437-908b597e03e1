#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务数据库模型
"""

import json
import time
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, Column, Integer, String, Text, Float, Date, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .config import config, ensure_data_directory

Base = declarative_base()


class LogRecord(Base):
    """日志记录表"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(Float, nullable=False, index=True)  # Unix时间戳
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    module = Column(String(100), nullable=True)  # 模块名称
    function = Column(String(100), nullable=True)  # 函数名称
    message = Column(Text, nullable=False)  # 日志消息
    context = Column(Text, nullable=True)  # 上下文信息
    user_id = Column(String(100), nullable=True)  # 用户ID
    request_id = Column(String(100), nullable=True)  # 请求ID
    metadata = Column(Text, nullable=True)  # 额外元数据(JSON格式)
    created_at = Column(Float, nullable=False, default=time.time)  # 创建时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_service_level_time', 'service_name', 'level', 'timestamp'),
        Index('idx_time_level', 'timestamp', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
            "level": self.level,
            "service_name": self.service_name,
            "module": self.module,
            "function": self.function,
            "message": self.message,
            "context": self.context,
            "user_id": self.user_id,
            "request_id": self.request_id,
            "metadata": json.loads(self.metadata) if self.metadata else None,
            "created_at": self.created_at
        }


class LogStats(Base):
    """日志统计表"""
    __tablename__ = "log_stats"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, nullable=False, index=True)  # 统计日期
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    count = Column(Integer, nullable=False, default=0)  # 日志数量
    updated_at = Column(Float, nullable=False, default=time.time)  # 更新时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_date_service_level', 'date', 'service_name', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "date": self.date.isoformat(),
            "service_name": self.service_name,
            "level": self.level,
            "count": self.count,
            "updated_at": self.updated_at
        }


class LogDatabase:
    """日志数据库管理类"""
    
    def __init__(self):
        # 确保数据目录存在
        ensure_data_directory()
        
        # 创建数据库引擎
        self.engine = create_engine(
            config.database_url,
            echo=False,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建数据库表"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ 日志数据库表创建完成")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()

    @contextmanager
    def get_session_context(self):
        """获取数据库会话上下文管理器"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def add_log(self, level: str, service_name: str, message: str, 
                module: str = None, function: str = None, context: str = None,
                user_id: str = None, request_id: str = None, metadata: Dict = None) -> int:
        """添加日志记录"""
        with self.get_session() as session:
            log_record = LogRecord(
                timestamp=time.time(),
                level=level.upper(),
                service_name=service_name,
                module=module,
                function=function,
                message=message,
                context=context,
                user_id=user_id,
                request_id=request_id,
                metadata=json.dumps(metadata) if metadata else None
            )
            
            session.add(log_record)
            session.commit()
            session.refresh(log_record)
            
            # 更新统计
            self._update_stats(session, log_record)
            
            return log_record.id

    def add_logs_batch(self, log_data_list: List[Dict[str, Any]]) -> List[int]:
        """批量添加日志记录"""
        try:
            with self.get_session_context() as session:
                log_records = []

                for log_data in log_data_list:
                    log_record = LogRecord(
                        timestamp=time.time(),
                        level=log_data.get('level', 'INFO').upper(),
                        service_name=log_data.get('service_name', ''),
                        module=log_data.get('module'),
                        function=log_data.get('function'),
                        message=log_data.get('message', ''),
                        context=log_data.get('context'),
                        user_id=log_data.get('user_id'),
                        request_id=log_data.get('request_id'),
                        metadata=json.dumps(log_data.get('metadata')) if log_data.get('metadata') else None
                    )
                    log_records.append(log_record)

                # 批量插入
                session.add_all(log_records)
                session.flush()  # 获取ID但不提交

                # 获取ID列表
                log_ids = [record.id for record in log_records]

                # 批量更新统计
                self._update_stats_batch(session, log_records)

                return log_ids

        except SQLAlchemyError as e:
            print(f"❌ 批量添加日志失败: {e}")
            raise e
    
    def _update_stats(self, session: Session, log_record: LogRecord):
        """更新日志统计"""
        today = date.fromtimestamp(log_record.timestamp)
        
        # 查找现有统计记录
        stats = session.query(LogStats).filter(
            LogStats.date == today,
            LogStats.service_name == log_record.service_name,
            LogStats.level == log_record.level
        ).first()
        
        if stats:
            stats.count += 1
            stats.updated_at = time.time()
        else:
            stats = LogStats(
                date=today,
                service_name=log_record.service_name,
                level=log_record.level,
                count=1
            )
            session.add(stats)
        
        session.commit()

    def _update_stats_batch(self, session: Session, log_records: List[LogRecord]):
        """批量更新日志统计"""
        # 按日期、服务、级别分组统计
        stats_map = {}

        for log_record in log_records:
            today = date.fromtimestamp(log_record.timestamp)
            key = (today, log_record.service_name, log_record.level)

            if key not in stats_map:
                stats_map[key] = 0
            stats_map[key] += 1

        # 更新或创建统计记录
        for (stat_date, service_name, level), count in stats_map.items():
            stats = session.query(LogStats).filter(
                LogStats.date == stat_date,
                LogStats.service_name == service_name,
                LogStats.level == level
            ).first()

            if stats:
                stats.count += count
                stats.updated_at = time.time()
            else:
                stats = LogStats(
                    date=stat_date,
                    service_name=service_name,
                    level=level,
                    count=count
                )
                session.add(stats)
    
    def get_logs(self, start_time: float = None, end_time: float = None,
                 service_name: str = None, level: str = None,
                 limit: int = None, offset: int = 0) -> List[LogRecord]:
        """查询日志记录"""
        with self.get_session() as session:
            query = session.query(LogRecord)
            
            # 时间过滤
            if start_time:
                query = query.filter(LogRecord.timestamp >= start_time)
            if end_time:
                query = query.filter(LogRecord.timestamp <= end_time)
            
            # 服务过滤
            if service_name:
                query = query.filter(LogRecord.service_name == service_name)
            
            # 级别过滤
            if level:
                query = query.filter(LogRecord.level == level.upper())
            
            # 排序和分页
            query = query.order_by(LogRecord.timestamp.desc())
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    def get_log_count(self, start_time: float = None, end_time: float = None,
                      service_name: str = None, level: str = None) -> int:
        """获取日志总数"""
        with self.get_session() as session:
            query = session.query(LogRecord)
            
            # 应用相同的过滤条件
            if start_time:
                query = query.filter(LogRecord.timestamp >= start_time)
            if end_time:
                query = query.filter(LogRecord.timestamp <= end_time)
            if service_name:
                query = query.filter(LogRecord.service_name == service_name)
            if level:
                query = query.filter(LogRecord.level == level.upper())
            
            return query.count()
    
    def get_services(self) -> List[str]:
        """获取所有服务名称列表"""
        with self.get_session() as session:
            result = session.query(LogRecord.service_name).distinct().all()
            return [row[0] for row in result]
    
    def get_stats(self, start_date: date = None, end_date: date = None,
                  service_name: str = None) -> List[LogStats]:
        """获取日志统计"""
        with self.get_session() as session:
            query = session.query(LogStats)
            
            # 日期过滤
            if start_date:
                query = query.filter(LogStats.date >= start_date)
            if end_date:
                query = query.filter(LogStats.date <= end_date)
            
            # 服务过滤
            if service_name:
                query = query.filter(LogStats.service_name == service_name)
            
            # 排序
            query = query.order_by(LogStats.date.desc(), LogStats.service_name, LogStats.level)
            
            return query.all()
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        with self.get_session() as session:
            # 总日志数
            total_logs = session.query(LogRecord).count()
            
            # 按级别统计
            level_stats = {}
            for level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                count = session.query(LogRecord).filter(LogRecord.level == level).count()
                level_stats[level] = count
            
            # 按服务统计
            service_stats = {}
            services = self.get_services()
            for service in services:
                count = session.query(LogRecord).filter(LogRecord.service_name == service).count()
                service_stats[service] = count
            
            # 最新日志时间
            latest_log = session.query(LogRecord).order_by(LogRecord.timestamp.desc()).first()
            latest_time = latest_log.timestamp if latest_log else None
            
            return {
                "total_logs": total_logs,
                "level_stats": level_stats,
                "service_stats": service_stats,
                "latest_log_time": latest_time,
                "services_count": len(services)
            }

    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            with self.get_session() as session:
                # 获取表信息
                tables_info = {}

                # 日志表信息
                log_count = session.query(LogRecord).count()
                tables_info['logs'] = {
                    'count': log_count,
                    'table_name': 'logs'
                }

                # 统计表信息
                stats_count = session.query(LogStats).count()
                tables_info['log_stats'] = {
                    'count': stats_count,
                    'table_name': 'log_stats'
                }

                return {
                    'database_path': config.database_path,
                    'database_url': config.database_url,
                    'tables': tables_info,
                    'total_records': log_count + stats_count
                }

        except SQLAlchemyError as e:
            print(f"❌ 获取数据库信息失败: {e}")
            return {}

    def cleanup_old_logs(self, before_timestamp: float, dry_run: bool = True) -> Dict[str, Any]:
        """清理旧日志（预留功能）"""
        try:
            with self.get_session() as session:
                # 查询要删除的日志数量
                query = session.query(LogRecord).filter(LogRecord.timestamp < before_timestamp)
                count = query.count()

                if dry_run:
                    return {
                        'dry_run': True,
                        'would_delete': count,
                        'before_timestamp': before_timestamp,
                        'before_datetime': datetime.fromtimestamp(before_timestamp).isoformat()
                    }
                else:
                    # 实际删除（暂不实现，建议使用数据库工具）
                    return {
                        'dry_run': False,
                        'message': '建议使用数据库管理工具进行清理',
                        'count': count
                    }

        except SQLAlchemyError as e:
            print(f"❌ 清理日志失败: {e}")
            return {'error': str(e)}

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
                return True
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False
