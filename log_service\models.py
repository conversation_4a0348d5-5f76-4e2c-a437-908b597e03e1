#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务数据库模型
"""

import json
import time
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy import create_engine, Column, Integer, String, Text, Float, Date, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

from .config import config, ensure_data_directory

Base = declarative_base()


class LogRecord(Base):
    """日志记录表"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(Float, nullable=False, index=True)  # Unix时间戳
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    module = Column(String(100), nullable=True)  # 模块名称
    function = Column(String(100), nullable=True)  # 函数名称
    message = Column(Text, nullable=False)  # 日志消息
    context = Column(Text, nullable=True)  # 上下文信息
    user_id = Column(String(100), nullable=True)  # 用户ID
    request_id = Column(String(100), nullable=True)  # 请求ID
    metadata = Column(Text, nullable=True)  # 额外元数据(JSON格式)
    created_at = Column(Float, nullable=False, default=time.time)  # 创建时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_service_level_time', 'service_name', 'level', 'timestamp'),
        Index('idx_time_level', 'timestamp', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
            "level": self.level,
            "service_name": self.service_name,
            "module": self.module,
            "function": self.function,
            "message": self.message,
            "context": self.context,
            "user_id": self.user_id,
            "request_id": self.request_id,
            "metadata": json.loads(self.metadata) if self.metadata else None,
            "created_at": self.created_at
        }


class LogStats(Base):
    """日志统计表"""
    __tablename__ = "log_stats"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, nullable=False, index=True)  # 统计日期
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    count = Column(Integer, nullable=False, default=0)  # 日志数量
    updated_at = Column(Float, nullable=False, default=time.time)  # 更新时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_date_service_level', 'date', 'service_name', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "date": self.date.isoformat(),
            "service_name": self.service_name,
            "level": self.level,
            "count": self.count,
            "updated_at": self.updated_at
        }


class LogDatabase:
    """日志数据库管理类"""
    
    def __init__(self):
        # 确保数据目录存在
        ensure_data_directory()
        
        # 创建数据库引擎
        self.engine = create_engine(
            config.database_url,
            echo=False,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建数据库表"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ 日志数据库表创建完成")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def add_log(self, level: str, service_name: str, message: str, 
                module: str = None, function: str = None, context: str = None,
                user_id: str = None, request_id: str = None, metadata: Dict = None) -> int:
        """添加日志记录"""
        with self.get_session() as session:
            log_record = LogRecord(
                timestamp=time.time(),
                level=level.upper(),
                service_name=service_name,
                module=module,
                function=function,
                message=message,
                context=context,
                user_id=user_id,
                request_id=request_id,
                metadata=json.dumps(metadata) if metadata else None
            )
            
            session.add(log_record)
            session.commit()
            session.refresh(log_record)
            
            # 更新统计
            self._update_stats(session, log_record)
            
            return log_record.id
    
    def _update_stats(self, session: Session, log_record: LogRecord):
        """更新日志统计"""
        today = date.fromtimestamp(log_record.timestamp)
        
        # 查找现有统计记录
        stats = session.query(LogStats).filter(
            LogStats.date == today,
            LogStats.service_name == log_record.service_name,
            LogStats.level == log_record.level
        ).first()
        
        if stats:
            stats.count += 1
            stats.updated_at = time.time()
        else:
            stats = LogStats(
                date=today,
                service_name=log_record.service_name,
                level=log_record.level,
                count=1
            )
            session.add(stats)
        
        session.commit()
    
    def get_logs(self, start_time: float = None, end_time: float = None,
                 service_name: str = None, level: str = None,
                 limit: int = None, offset: int = 0) -> List[LogRecord]:
        """查询日志记录"""
        with self.get_session() as session:
            query = session.query(LogRecord)
            
            # 时间过滤
            if start_time:
                query = query.filter(LogRecord.timestamp >= start_time)
            if end_time:
                query = query.filter(LogRecord.timestamp <= end_time)
            
            # 服务过滤
            if service_name:
                query = query.filter(LogRecord.service_name == service_name)
            
            # 级别过滤
            if level:
                query = query.filter(LogRecord.level == level.upper())
            
            # 排序和分页
            query = query.order_by(LogRecord.timestamp.desc())
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    def get_log_count(self, start_time: float = None, end_time: float = None,
                      service_name: str = None, level: str = None) -> int:
        """获取日志总数"""
        with self.get_session() as session:
            query = session.query(LogRecord)
            
            # 应用相同的过滤条件
            if start_time:
                query = query.filter(LogRecord.timestamp >= start_time)
            if end_time:
                query = query.filter(LogRecord.timestamp <= end_time)
            if service_name:
                query = query.filter(LogRecord.service_name == service_name)
            if level:
                query = query.filter(LogRecord.level == level.upper())
            
            return query.count()
    
    def get_services(self) -> List[str]:
        """获取所有服务名称列表"""
        with self.get_session() as session:
            result = session.query(LogRecord.service_name).distinct().all()
            return [row[0] for row in result]
    
    def get_stats(self, start_date: date = None, end_date: date = None,
                  service_name: str = None) -> List[LogStats]:
        """获取日志统计"""
        with self.get_session() as session:
            query = session.query(LogStats)
            
            # 日期过滤
            if start_date:
                query = query.filter(LogStats.date >= start_date)
            if end_date:
                query = query.filter(LogStats.date <= end_date)
            
            # 服务过滤
            if service_name:
                query = query.filter(LogStats.service_name == service_name)
            
            # 排序
            query = query.order_by(LogStats.date.desc(), LogStats.service_name, LogStats.level)
            
            return query.all()
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        with self.get_session() as session:
            # 总日志数
            total_logs = session.query(LogRecord).count()
            
            # 按级别统计
            level_stats = {}
            for level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                count = session.query(LogRecord).filter(LogRecord.level == level).count()
                level_stats[level] = count
            
            # 按服务统计
            service_stats = {}
            services = self.get_services()
            for service in services:
                count = session.query(LogRecord).filter(LogRecord.service_name == service).count()
                service_stats[service] = count
            
            # 最新日志时间
            latest_log = session.query(LogRecord).order_by(LogRecord.timestamp.desc()).first()
            latest_time = latest_log.timestamp if latest_log else None
            
            return {
                "total_logs": total_logs,
                "level_stats": level_stats,
                "service_stats": service_stats,
                "latest_log_time": latest_time,
                "services_count": len(services)
            }
