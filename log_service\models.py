#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务数据库模型
"""

import json
import time
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from sqlalchemy import create_engine, Column, Integer, String, Text, Float, Date, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .config import config, ensure_data_directory

Base = declarative_base()


class LogRecord(Base):
    """日志记录表"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(Float, nullable=False, index=True)  # Unix时间戳
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    module = Column(String(100), nullable=True)  # 模块名称
    function = Column(String(100), nullable=True)  # 函数名称
    message = Column(Text, nullable=False)  # 日志消息
    context = Column(Text, nullable=True)  # 上下文信息
    user_id = Column(String(100), nullable=True)  # 用户ID
    request_id = Column(String(100), nullable=True)  # 请求ID
    metadata = Column(Text, nullable=True)  # 额外元数据(JSON格式)
    created_at = Column(Float, nullable=False, default=time.time)  # 创建时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_service_level_time', 'service_name', 'level', 'timestamp'),
        Index('idx_time_level', 'timestamp', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
            "level": self.level,
            "service_name": self.service_name,
            "module": self.module,
            "function": self.function,
            "message": self.message,
            "context": self.context,
            "user_id": self.user_id,
            "request_id": self.request_id,
            "metadata": json.loads(self.metadata) if self.metadata else None,
            "created_at": self.created_at
        }


class LogStats(Base):
    """日志统计表"""
    __tablename__ = "log_stats"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(Date, nullable=False, index=True)  # 统计日期
    service_name = Column(String(100), nullable=False, index=True)  # 服务名称
    level = Column(String(20), nullable=False, index=True)  # 日志级别
    count = Column(Integer, nullable=False, default=0)  # 日志数量
    updated_at = Column(Float, nullable=False, default=time.time)  # 更新时间
    
    # 复合索引
    __table_args__ = (
        Index('idx_date_service_level', 'date', 'service_name', 'level'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "date": self.date.isoformat(),
            "service_name": self.service_name,
            "level": self.level,
            "count": self.count,
            "updated_at": self.updated_at
        }


class LogDatabase:
    """日志数据库管理类"""
    
    def __init__(self):
        # 确保数据目录存在
        ensure_data_directory()
        
        # 创建数据库引擎
        self.engine = create_engine(
            config.database_url,
            echo=False,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建数据库表"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ 日志数据库表创建完成")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()

    @contextmanager
    def get_session_context(self):
        """获取数据库会话上下文管理器"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def add_log(self, level: str, service_name: str, message: str,
                module: str = None, function: str = None, context: str = None) -> int:
        """添加日志记录 - 简化版"""
        with self.get_session() as session:
            log_record = LogRecord(
                timestamp=time.time(),
                level=level.upper(),
                service_name=service_name,
                module=module,
                function=function,
                message=message,
                context=context
            )

            session.add(log_record)
            session.commit()
            session.refresh(log_record)

            return log_record.id

    # 移除复杂的批量操作和统计功能，专注核心日志记录
    
    def get_logs(self, start_time: float = None, end_time: float = None,
                 service_name: str = None, level: str = None,
                 limit: int = None, offset: int = 0) -> List[LogRecord]:
        """查询日志记录"""
        with self.get_session() as session:
            query = session.query(LogRecord)
            
            # 时间过滤
            if start_time:
                query = query.filter(LogRecord.timestamp >= start_time)
            if end_time:
                query = query.filter(LogRecord.timestamp <= end_time)
            
            # 服务过滤
            if service_name:
                query = query.filter(LogRecord.service_name == service_name)
            
            # 级别过滤
            if level:
                query = query.filter(LogRecord.level == level.upper())
            
            # 排序和分页
            query = query.order_by(LogRecord.timestamp.desc())
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    # 简化版本，移除复杂的统计和管理功能
    # 如需要这些功能，可以通过数据库管理工具实现
