#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务主服务类 - 简化版
专注核心功能：快速记录和基础查询
"""

import time
from typing import Optional
from fastapi import HTTPException, Query

from services.base_service import BaseService
from .config import config
from .models import LogDatabase
from .schemas import LogRequest, validate_log_level, create_success_response, create_error_response


class LogService(BaseService):
    """日志服务类"""
    
    def __init__(self):
        super().__init__(config.service_name, config.service_port)
        
        # 初始化数据库
        self.db = LogDatabase()
        
        self.setup_log_routes()
    
    def setup_log_routes(self):
        """设置日志相关路由"""

        @self.app.post("/logs")
        async def create_log(request: LogRequest):
            """记录日志 - 核心功能"""
            try:
                # 验证日志级别
                level = validate_log_level(request.level)

                # 记录日志
                log_id = self.db.add_log(
                    level=level,
                    service_name=request.service_name,
                    message=request.message,
                    module=request.module,
                    function=request.function,
                    context=request.context
                )

                return create_success_response({
                    "log_id": log_id,
                    "level": level,
                    "service_name": request.service_name
                }, "日志记录成功")

            except Exception as e:
                print(f"❌ 记录日志失败: {e}")
                return create_error_response(f"记录日志失败: {str(e)}")
        
        @self.app.get("/logs")
        async def get_logs(
            service_name: Optional[str] = Query(None, description="服务名称"),
            level: Optional[str] = Query(None, description="日志级别"),
            limit: int = Query(50, description="返回数量限制", ge=1, le=500)
        ):
            """查询日志 - 基础功能"""
            try:
                # 验证日志级别
                if level:
                    level = validate_log_level(level)

                # 查询日志
                logs = self.db.get_logs(
                    service_name=service_name,
                    level=level,
                    limit=limit
                )

                # 转换为字典
                log_list = [log.to_dict() for log in logs]

                return create_success_response({
                    "logs": log_list,
                    "count": len(log_list)
                }, f"查询到 {len(log_list)} 条日志")

            except Exception as e:
                print(f"❌ 查询日志失败: {e}")
                return create_error_response(f"查询日志失败: {str(e)}")
        
        # 简化版本，移除复杂的统计和管理功能
        # 专注核心：记录日志、查询日志
