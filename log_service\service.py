#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务主服务类
"""

import time
from typing import Dict, List, Optional
from datetime import date, datetime
from fastapi import HTTPException, Query, Depends

from services.base_service import BaseService
from .config import config
from .models import LogDatabase
from .schemas import (
    LogRequest, LogQueryRequest, StatsQueryRequest,
    LogCreateResponse, LogListResponse, StatsListResponse,
    ServicesListResponse, SummaryStatsResponse
)


class LogService(BaseService):
    """日志服务类"""
    
    def __init__(self):
        super().__init__(config.service_name, config.service_port)
        
        # 初始化数据库
        self.db = LogDatabase()
        
        self.setup_log_routes()
    
    def setup_log_routes(self):
        """设置日志相关路由"""
        
        @self.app.post("/logs", response_model=LogCreateResponse)
        async def create_log(request: LogRequest):
            """记录日志"""
            try:
                log_id = self.db.add_log(
                    level=request.level,
                    service_name=request.service_name,
                    message=request.message,
                    module=request.module,
                    function=request.function,
                    context=request.context,
                    user_id=request.user_id,
                    request_id=request.request_id,
                    metadata=request.metadata
                )
                
                return LogCreateResponse(
                    success=True,
                    data={
                        "log_id": log_id,
                        "level": request.level,
                        "service_name": request.service_name,
                        "message": "日志记录成功"
                    },
                    message="日志记录成功",
                    timestamp=time.time()
                )
                
            except Exception as e:
                print(f"❌ 记录日志失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/logs", response_model=LogListResponse)
        async def get_logs(
            start_time: Optional[float] = Query(None, description="开始时间戳"),
            end_time: Optional[float] = Query(None, description="结束时间戳"),
            service_name: Optional[str] = Query(None, description="服务名称"),
            level: Optional[str] = Query(None, description="日志级别"),
            limit: int = Query(50, description="返回数量限制", ge=1, le=1000),
            offset: int = Query(0, description="分页偏移量", ge=0)
        ):
            """查询日志"""
            try:
                # 验证日志级别
                if level:
                    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
                    if level.upper() not in valid_levels:
                        raise HTTPException(
                            status_code=400, 
                            detail=f"无效的日志级别: {level}，有效值: {', '.join(valid_levels)}"
                        )
                    level = level.upper()
                
                # 查询日志
                logs = self.db.get_logs(
                    start_time=start_time,
                    end_time=end_time,
                    service_name=service_name,
                    level=level,
                    limit=limit,
                    offset=offset
                )
                
                # 获取总数
                total = self.db.get_log_count(
                    start_time=start_time,
                    end_time=end_time,
                    service_name=service_name,
                    level=level
                )
                
                # 转换为字典
                log_list = [log.to_dict() for log in logs]
                
                return LogListResponse(
                    success=True,
                    data={
                        "logs": log_list,
                        "total": total,
                        "limit": limit,
                        "offset": offset,
                        "has_more": offset + len(log_list) < total,
                        "returned": len(log_list)
                    },
                    message=f"查询到 {len(log_list)} 条日志记录",
                    timestamp=time.time()
                )
                
            except HTTPException:
                raise
            except Exception as e:
                print(f"❌ 查询日志失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/logs/stats", response_model=StatsListResponse)
        async def get_stats(
            start_date: Optional[date] = Query(None, description="开始日期"),
            end_date: Optional[date] = Query(None, description="结束日期"),
            service_name: Optional[str] = Query(None, description="服务名称")
        ):
            """获取日志统计"""
            try:
                # 查询统计数据
                stats = self.db.get_stats(
                    start_date=start_date,
                    end_date=end_date,
                    service_name=service_name
                )
                
                # 转换为字典
                stats_list = [stat.to_dict() for stat in stats]
                
                # 计算汇总信息
                summary = {
                    "total_records": len(stats_list),
                    "date_range": {
                        "start": start_date.isoformat() if start_date else None,
                        "end": end_date.isoformat() if end_date else None
                    },
                    "service_filter": service_name
                }
                
                # 按级别汇总
                level_summary = {}
                for stat in stats_list:
                    level = stat["level"]
                    if level not in level_summary:
                        level_summary[level] = 0
                    level_summary[level] += stat["count"]
                
                summary["level_summary"] = level_summary
                
                return StatsListResponse(
                    success=True,
                    data={
                        "stats": stats_list,
                        "summary": summary
                    },
                    message=f"获取到 {len(stats_list)} 条统计记录",
                    timestamp=time.time()
                )
                
            except Exception as e:
                print(f"❌ 获取统计数据失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/logs/services", response_model=ServicesListResponse)
        async def get_services():
            """获取服务列表"""
            try:
                services = self.db.get_services()
                
                return ServicesListResponse(
                    success=True,
                    data={
                        "services": services,
                        "count": len(services)
                    },
                    message=f"获取到 {len(services)} 个服务",
                    timestamp=time.time()
                )
                
            except Exception as e:
                print(f"❌ 获取服务列表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/logs/summary", response_model=SummaryStatsResponse)
        async def get_summary():
            """获取汇总统计"""
            try:
                summary = self.db.get_summary_stats()
                
                # 添加时间信息
                if summary["latest_log_time"]:
                    summary["latest_log_datetime"] = datetime.fromtimestamp(
                        summary["latest_log_time"]
                    ).isoformat()
                
                return SummaryStatsResponse(
                    success=True,
                    data=summary,
                    message="获取汇总统计成功",
                    timestamp=time.time()
                )
                
            except Exception as e:
                print(f"❌ 获取汇总统计失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.delete("/logs/cleanup")
        async def cleanup_logs(
            before_time: Optional[float] = Query(None, description="清理此时间之前的日志"),
            service_name: Optional[str] = Query(None, description="指定服务名称"),
            level: Optional[str] = Query(None, description="指定日志级别"),
            dry_run: bool = Query(True, description="是否为试运行模式")
        ):
            """清理日志（预留接口，建议使用数据库管理工具）"""
            try:
                # 这里只是一个预留接口，实际建议使用数据库管理工具进行清理
                if dry_run:
                    # 试运行模式，只统计不删除
                    count = self.db.get_log_count(
                        end_time=before_time,
                        service_name=service_name,
                        level=level
                    )
                    
                    return {
                        "success": True,
                        "data": {
                            "dry_run": True,
                            "would_delete": count,
                            "message": "试运行模式，未实际删除"
                        },
                        "message": f"试运行：将删除 {count} 条日志",
                        "timestamp": time.time()
                    }
                else:
                    # 实际删除模式暂不实现，建议使用数据库工具
                    return {
                        "success": False,
                        "data": {
                            "message": "建议使用数据库管理工具进行日志清理"
                        },
                        "message": "清理功能暂未实现，请使用数据库管理工具",
                        "timestamp": time.time()
                    }
                
            except Exception as e:
                print(f"❌ 清理日志失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
