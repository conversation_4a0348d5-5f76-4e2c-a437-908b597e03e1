# AI对话服务 API文档

Yuan项目AI对话服务的完整API接口文档。

## 基础信息

- **服务名称**: chat_service
- **服务端口**: 8002
- **基础URL**: http://127.0.0.1:8002
- **API文档**: http://127.0.0.1:8002/docs
- **版本**: 1.0.0

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "timestamp": **********.123
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误类型",
    "message": "错误描述",
    "timestamp": **********.123
}
```

## API接口列表

### 1. AI对话接口

#### POST /chat
进行AI对话，支持流式和非流式响应。

**请求参数**:
```json
{
    "conversation_id": "string (可选)",
    "message": "string (必需)",
    "user_id": "string (可选)",
    "stream": "boolean (可选，默认false)",
    "model": "string (可选，默认gpt-4o)",
    "temperature": "number (可选，0-2)",
    "max_tokens": "number (可选)",
    "top_p": "number (可选，0-1)",
    "frequency_penalty": "number (可选，-2到2)",
    "presence_penalty": "number (可选，-2到2)"
}
```

**响应示例**:
- 非流式响应:
```json
{
    "success": true,
    "data": {
        "conversation_id": "uuid-string",
        "response": "AI回复内容",
        "message_count": 3
    },
    "message": "对话成功",
    "timestamp": **********.123
}
```

- 流式响应:
```
data: AI回复内容片段1
data: AI回复内容片段2
...
data: [DONE]
```

**错误码**:
- 404: 对话不存在
- 500: 服务器内部错误

### 2. 对话管理接口

#### POST /conversations
创建新对话。

**请求参数**:
```json
{
    "title": "string (可选)",
    "user_id": "string (可选)",
    "metadata": "object (可选)"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "conversation_id": "uuid-string",
        "title": "对话标题",
        "user_id": "user123",
        "created_at": **********.123,
        "updated_at": **********.123,
        "message_count": 0,
        "metadata": {}
    },
    "message": "对话创建成功",
    "timestamp": **********.123
}
```

#### GET /conversations
获取对话列表。

**查询参数**:
- `user_id`: string (可选) - 用户ID过滤
- `limit`: number (可选，默认50) - 返回数量限制

**响应示例**:
```json
{
    "success": true,
    "data": {
        "conversations": [
            {
                "id": 1,
                "conversation_id": "uuid-string",
                "title": "对话标题",
                "user_id": "user123",
                "created_at": **********.123,
                "updated_at": **********.123,
                "message_count": 5,
                "metadata": {}
            }
        ],
        "count": 1
    },
    "message": "获取对话列表成功",
    "timestamp": **********.123
}
```

#### GET /conversations/{conversation_id}
获取对话详情。

**路径参数**:
- `conversation_id`: string (必需) - 对话ID

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "conversation_id": "uuid-string",
        "title": "对话标题",
        "user_id": "user123",
        "created_at": **********.123,
        "updated_at": **********.123,
        "message_count": 5,
        "metadata": {}
    },
    "message": "获取对话详情成功",
    "timestamp": **********.123
}
```

**错误码**:
- 404: 对话不存在

#### PUT /conversations/{conversation_id}
更新对话信息。

**路径参数**:
- `conversation_id`: string (必需) - 对话ID

**请求参数**:
```json
{
    "title": "string (可选)",
    "metadata": "object (可选)"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "conversation_id": "uuid-string",
        "title": "新标题",
        "user_id": "user123",
        "created_at": **********.123,
        "updated_at": **********.456,
        "message_count": 5,
        "metadata": {"tag": "更新"}
    },
    "message": "对话更新成功",
    "timestamp": **********.456
}
```

**错误码**:
- 404: 对话不存在

#### DELETE /conversations/{conversation_id}
删除对话及其所有消息。

**路径参数**:
- `conversation_id`: string (必需) - 对话ID

**响应示例**:
```json
{
    "success": true,
    "message": "对话 uuid-string 删除成功",
    "timestamp": **********.123
}
```

**错误码**:
- 404: 对话不存在

#### GET /conversations/{conversation_id}/messages
获取对话消息列表。

**路径参数**:
- `conversation_id`: string (必需) - 对话ID

**查询参数**:
- `limit`: number (可选) - 返回数量限制

**响应示例**:
```json
{
    "success": true,
    "data": {
        "conversation_id": "uuid-string",
        "messages": [
            {
                "id": 1,
                "conversation_id": "uuid-string",
                "role": "user",
                "content": "用户消息内容",
                "created_at": **********.123,
                "metadata": {}
            },
            {
                "id": 2,
                "conversation_id": "uuid-string",
                "role": "assistant",
                "content": "AI回复内容",
                "created_at": **********.456,
                "metadata": {}
            }
        ],
        "count": 2
    },
    "message": "获取消息列表成功",
    "timestamp": **********.789
}
```

**错误码**:
- 404: 对话不存在

### 3. 统计信息接口

#### GET /stats
获取对话统计信息。

**响应示例**:
```json
{
    "success": true,
    "data": {
        "total_conversations": 10,
        "total_messages": 50
    },
    "message": "获取统计信息成功",
    "timestamp": **********.123
}
```

### 4. 系统接口

#### GET /health
健康检查接口。

**响应示例**:
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "service": "chat_service",
        "service_id": "service-uuid",
        "host": "127.0.0.1",
        "port": 8002
    },
    "message": "服务运行正常",
    "timestamp": **********.123
}
```

#### GET /info
服务信息接口。

**响应示例**:
```json
{
    "success": true,
    "data": {
        "service_name": "chat_service",
        "service_id": "service-uuid",
        "host": "127.0.0.1",
        "port": 8002,
        "registry_url": "http://127.0.0.1:8500",
        "health_check_url": "http://127.0.0.1:8002/health",
        "version": "1.0.0",
        "capabilities": ["服务注册", "服务发现", "心跳监控", "健康检查"]
    },
    "message": "获取服务信息成功",
    "timestamp": **********.123
}
```

## 调用示例

### Python调用示例

```python
import httpx
import asyncio

async def chat_example():
    """简单对话示例"""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://127.0.0.1:8002/chat",
            json={
                "message": "你好，请介绍一下你自己",
                "stream": False
            }
        )
        return response.json()

async def stream_chat_example():
    """流式对话示例"""
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://127.0.0.1:8002/chat",
            json={
                "message": "写一首关于春天的诗",
                "stream": True
            }
        ) as response:
            async for chunk in response.aiter_text():
                if chunk.startswith("data: "):
                    content = chunk[6:]
                    if content != "[DONE]":
                        print(content, end="", flush=True)

async def conversation_management_example():
    """对话管理示例"""
    async with httpx.AsyncClient() as client:
        # 创建对话
        conv_response = await client.post(
            "http://127.0.0.1:8002/conversations",
            json={"title": "测试对话", "user_id": "user123"}
        )
        conversation_id = conv_response.json()["data"]["conversation_id"]
        
        # 发送消息
        await client.post(
            "http://127.0.0.1:8002/chat",
            json={
                "conversation_id": conversation_id,
                "message": "你好",
                "stream": False
            }
        )
        
        # 获取消息历史
        messages_response = await client.get(
            f"http://127.0.0.1:8002/conversations/{conversation_id}/messages"
        )
        return messages_response.json()

# 运行示例
# asyncio.run(chat_example())
```

## 错误处理

### 常见错误码
- **400**: 请求参数错误
- **404**: 资源不存在
- **500**: 服务器内部错误

### 错误处理建议
1. 检查请求参数格式和类型
2. 确认对话ID存在
3. 检查网络连接
4. 查看服务日志获取详细错误信息

## 版本变更记录

### v1.0.0 (当前版本)
- 初始版本发布
- 实现AI对话功能
- 实现对话管理功能
- 实现统计信息功能
- 支持流式和非流式响应
- 支持多用户隔离

## 注意事项

1. **API密钥**: 确保OpenAI API密钥配置正确
2. **流式响应**: 客户端需要正确处理流式数据格式
3. **会话管理**: 合理设置会话超时和清理策略
4. **资源限制**: 注意AI API的调用频率和成本控制
5. **数据备份**: 定期备份对话数据
