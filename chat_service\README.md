# AI对话服务 (Chat Service)

Yuan项目的AI对话微服务，提供智能对话、会话管理、对话历史等功能。

## 功能特性

### 核心功能
- **AI对话**：支持流式和非流式AI对话
- **会话管理**：创建、更新、删除对话会话
- **对话历史**：保存和查询对话历史记录
- **多用户支持**：支持多用户隔离的对话管理
- **参数自定义**：支持自定义AI模型参数

### 技术特性
- **微服务架构**：继承BaseService，自动服务注册
- **独立数据库**：使用SQLite独立存储对话数据
- **流式响应**：支持实时流式对话体验
- **RESTful API**：标准化的API接口设计
- **统一响应格式**：符合项目开发规范

## API接口

详细的API接口文档请参考：[API.md](./API.md)

### 主要接口
- **AI对话**: `POST /chat` - 进行AI对话，支持流式和非流式响应
- **创建对话**: `POST /conversations` - 创建新的对话会话
- **对话列表**: `GET /conversations` - 获取对话列表
- **对话详情**: `GET /conversations/{id}` - 获取对话详情
- **更新对话**: `PUT /conversations/{id}` - 更新对话信息
- **删除对话**: `DELETE /conversations/{id}` - 删除对话
- **消息列表**: `GET /conversations/{id}/messages` - 获取对话消息
- **统计信息**: `GET /stats` - 获取对话统计信息
- **健康检查**: `GET /health` - 服务健康检查
- **服务信息**: `GET /info` - 获取服务信息

## 数据模型

### 对话表 (conversations)
- `id`: 主键ID
- `conversation_id`: 对话唯一标识
- `title`: 对话标题
- `user_id`: 用户ID
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `message_count`: 消息数量
- `metadata`: 对话元数据

### 消息表 (messages)
- `id`: 主键ID
- `conversation_id`: 所属对话ID
- `role`: 消息角色 (user/assistant/system)
- `content`: 消息内容
- `created_at`: 创建时间
- `metadata`: 消息元数据

## 配置说明

### 环境变量
- `CHAT_SERVICE_HOST`: 服务主机地址（默认：127.0.0.1）
- `CHAT_SERVICE_PORT`: 服务端口（默认：8002）
- `CHAT_DATABASE_URL`: 数据库连接URL
- `CHAT_OPENAI_API_KEY`: OpenAI API密钥
- `CHAT_OPENAI_BASE_URL`: OpenAI API基础URL
- `CHAT_DEFAULT_MODEL`: 默认AI模型
- `CHAT_TEMPERATURE`: 默认温度参数
- `CHAT_MAX_TOKENS`: 默认最大token数

### AI参数配置
- **temperature**: 随机性控制 (0-2)
- **max_tokens**: 最大生成token数
- **top_p**: 核采样参数 (0-1)
- **frequency_penalty**: 频率惩罚 (-2到2)
- **presence_penalty**: 存在惩罚 (-2到2)

## 启动方式

### 直接启动
```bash
python -m chat_service.main
```

### 模块导入
```python
from chat_service import ChatService

service = ChatService()
service.run()
```

## 依赖关系

### 技术依赖
- FastAPI: Web框架
- SQLAlchemy: ORM框架
- OpenAI: AI对话客户端
- Pydantic: 数据验证

### 服务依赖
- **service_registry**: 服务注册中心（必需）
- **log_service**: 日志服务（可选）

## 使用示例

详细的调用示例请参考：[API.md](./API.md)

### 快速开始
```python
import httpx

# 简单对话
async def quick_chat():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://127.0.0.1:8002/chat",
            json={"message": "你好", "stream": False}
        )
        return response.json()
```

## 注意事项

1. **API密钥安全**：确保OpenAI API密钥安全存储
2. **数据库备份**：定期备份对话数据
3. **流式响应**：客户端需要正确处理流式数据
4. **会话管理**：合理设置会话超时和清理策略
5. **资源限制**：注意AI API的调用频率和成本控制
